name: <PERSON>uild and Push Docker Image to Google Artifact Registry

on:
  push:
    branches:
      - main

env:
  IMAGE_URL: ${{ vars.GCP_REPOSITORY_URL }}/${{vars.GCP_PROJECT_NAME}}:${{ github.sha }}
  REGION: europe-west2

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authentication
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev --quiet

      - name: Get Last Commit Message
        id: commit
        run: |
          echo "message=$(git log -1 --pretty=%B)" >> $GITHUB_OUTPUT

      - name: Build and Push Image
        run: |
          docker build --platform linux/amd64 -f ci/Dockerfile \
            --build-arg LAST_COMMIT_MESSAGE="${{ steps.commit.outputs.message }}" \
            -t ${{ env.IMAGE_URL }} .
          docker push ${{ env.IMAGE_URL }}

      - name: Restart GCE Instance
        run: |
          gcloud compute instances update-container --container-image=${{env.IMAGE_URL}}  ${{ vars.GCP_PROJECT_NAME }}-instance --zone=${{ env.REGION }}-a --project ${{ vars.GCP_PROJECT_ID }}
