{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "allowUnreachableCode": false, "alwaysStrict": true, "baseUrl": ".", "declaration": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "lib": ["ES2016"], "module": "NodeNext", "noEmit": false, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "dist", "paths": {"@/*": ["./src/*"]}, "preserveConstEnums": true, "removeComments": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": false, "strict": true, "strictNullChecks": true, "target": "ES2016", "moduleResolution": "nodenext"}, "exclude": ["node_modules", "node_modules/**/*", "dist", "out", "coverage", ".vscode", ".git"], "include": ["src/**/*.ts"]}