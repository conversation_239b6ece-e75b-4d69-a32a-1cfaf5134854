{"eslint.options": {"overrideConfigFile": "ci/.eslintrc.json", "ignorePath": "ci/.es<PERSON><PERSON><PERSON>"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "[ts][js]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": "ci/.prettierrc.json", "prettier.ignorePath": "ci/.prettierignore", "prettier.requireConfig": true, "files.exclude": {"**/.git": true, "**/.svn": true}, "search.exclude": {"**/node_modules": true, "dist": true}}