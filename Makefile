# Colors for terminal output
BLUE=\033[0;34m
GREEN=\033[0;32m
RED=\033[0;31m
YELLOW=\033[0;33m
NC=\033[0m # No Color

# Default shell
SHELL := /bin/bash

# Environment variables
export NODE_ENV ?= development

.PHONY: help install fix build clean check update dev start

# Show help by default
default: help

help: ## Show this help
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(BLUE)%-30s$(NC) %s\n", $$1, $$2}'

# Set Node version if .nvmrc is present
use_node:
		. $(HOME)/.nvm/nvm.sh && nvm use

# Installation
install: use_node ## Install dependencies
	@echo "$(BLUE)Installing dependencies...$(NC)"
	npm ci
	@echo "$(GREEN)Installation complete!$(NC)"

# Cleaning
clean: use_node ## Clean build artifacts and dependencies
	@echo "$(BLUE)Cleaning project...$(NC)"
	npm run clean
	@echo "$(GREEN)Clean complete!$(NC)"

# Code quality checks
check: use_node ## Run all code quality checks
	@echo "$(BLUE)Running code quality checks...$(NC)"
	npm run check
	@echo "$(GREEN)All checks passed!$(NC)"

# Fix code quality issues
fix: use_node ## Fix code quality issues
	@echo "$(BLUE)Fixing code quality issues...$(NC)"
	npm run fix
	@echo "$(GREEN)Fixes applied!$(NC)"


# Update dependencies
update: use_node ## Update dependencies interactively
	@echo "$(BLUE)Updating dependencies...$(NC)"
	npm run update-deps
	@echo "$(GREEN)Dependencies updated!$(NC)"

# Development server
dev: use_node ## Start development server
	@echo "$(BLUE)Starting development server...$(NC)"
	npm run dev
	@echo "$(GREEN)Development server running!$(NC)"

# Start production server
start: use_node ## Start production server
	@echo "$(BLUE)Starting production server...$(NC)"
	npm run start
	@echo "$(GREEN)Production server running!$(NC)"
