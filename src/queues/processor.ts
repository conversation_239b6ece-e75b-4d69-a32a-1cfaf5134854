import type { Job } from 'bullmq'

import { setJobCompleted, setJobRunning } from '../db.js'
import type { JobDb } from '../types.js'
import { execSyncWithErrorHandling } from '../utils/exec.js'
import { log } from '../utils/logger.js'
import { createBuildFlag, JOB_COMMAND_MAP, removeBuildFlag } from './helpers.js'

// Main job processor
export async function processJob(job: Job<JobDb>): Promise<string | void> {
  const { jobId, type, repo, storyId } = job.data
  let commands
  switch (type) {
    case 'DEPLOY':
      commands = await JOB_COMMAND_MAP.DEPLOY(repo)
      break
    case 'UPDATE':
      commands = JOB_COMMAND_MAP.UPDATE(repo)
      break
    case 'UPDATE_ONE':
      if (!storyId) throw new Error('Story ID is required for UPDATE_ONE jobs')
      commands = JOB_COMMAND_MAP.UPDATE_ONE(repo, storyId)
      break
    default:
      throw new Error('Unknown job type')
  }
  const startTime = Date.now()

  log('info', 'Job started', { jobId, repo, type })
  let output = ''

  try {
    log('info', 'Setting job to running state', { jobId, repo, type })
    setJobRunning(jobId)

    log('info', 'Removing build flag', { jobId, repo, type })
    await removeBuildFlag(repo)

    for (const { command, action, workDir } of commands) {
      log('info', 'Executing command', {
        details: JSON.stringify({ action, command, workDir }),
        jobId,
        repo,
        type
      })

      try {
        output += execSyncWithErrorHandling(command, action, repo, type, jobId, workDir ? { cwd: workDir } : undefined)
      } catch (cmdError) {
        log('error', 'Command execution failed', {
          details: JSON.stringify({ action, command, workDir }),
          error: cmdError,
          jobId,
          repo,
          type
        })
        throw cmdError
      }
    }

    log('info', 'Creating build flag', { jobId, repo, type })
    await createBuildFlag(repo)

    const duration = (Date.now() - startTime) / 1000

    log('info', 'Setting job to completed state', { jobId, repo, type })
    setJobCompleted(jobId, 'completed', output)

    log('info', 'Job completed successfully', {
      details: JSON.stringify({ output }),
      duration,
      jobId,
      repo,
      type
    })
    return output
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000
    const errorOutput = `${output}\n\nError: ${JSON.stringify(error, null, 2)}`

    log('error', 'Setting job to error state', {
      error: errorOutput,
      jobId,
      repo,
      type
    })

    setJobCompleted(jobId, 'error', errorOutput)

    log('error', 'Job failed', {
      details: JSON.stringify({ output }),
      duration,
      error: errorOutput,
      jobId,
      repo,
      type
    })

    throw error
  }
}
