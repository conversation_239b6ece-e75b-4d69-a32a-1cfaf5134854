import { Queue } from 'bullmq'

import type { JobDb } from '../types.js'

const connection = {
  host: '127.0.0.1',
  port: 6379
}

const defaultJobOptions = {
  attempts: 3,
  backoff: {
    delay: 30000,
    type: 'fixed'
  },
  removeOnComplete: 100,
  removeOnFail: 100
} as const

const QUEUE_NAMES = {
  CONCURRENT: 'concurrent',
  SEQUENTIAL: 'sequential'
} as const

export const queues = {
  concurrent: new Queue<JobDb>(QUEUE_NAMES.CONCURRENT, {
    connection,
    defaultJobOptions
  }),
  sequential: new Queue<JobDb>(QUEUE_NAMES.SEQUENTIAL, {
    connection,
    defaultJobOptions
  })
}
