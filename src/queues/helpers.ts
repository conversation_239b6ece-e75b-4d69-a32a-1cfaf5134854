import { unlinkSync, writeFileSync } from 'fs'

import { CONFIG, getBuildFlag, getWorkDir } from '../config.js'
import { JobCommand } from '../types.js'
import { getInstallationToken } from '../utils/githubToken.js'

// Command factories
const createDeployCommands = async (repo: string | undefined): Promise<JobCommand[]> => {
  if (!repo) throw new Error('Repository is required for deploy commands')
  const workDir = getWorkDir(repo)
  const token = await getInstallationToken()

  return [
    {
      action: 'Cleaning working directory',
      command: `rm -rf ${workDir}`
    },
    {
      action: 'Cloning repository',
      command: `git clone --depth 1 --single-branch --branch dev https://x-access-token:${token}@github.com/${CONFIG.GH_ORG}/${repo}.git ${workDir}`
    },
    {
      action: 'Installing dependencies',
      command:
        'npm install --ignore-scripts --unsafe-perm=true --allow-root --loglevel error --no-audit --maxsockets 50 --no-fund --no-update-notifier && chmod -R 755 node_modules && npm install --unsafe-perm=true --allow-root --loglevel error --no-audit --maxsockets 50 --no-fund --no-update-notifier',
      workDir
    },
    {
      action: 'Flash Assets',
      command: 'flash assets',
      workDir
    },
    {
      action: 'Flash Fetch',
      command: 'flash fetch',
      workDir
    },
    {
      action: 'Flash Build',
      command: 'flash build',
      workDir
    }
  ]
}

const createUpdateCommands = (repo: string): JobCommand[] => {
  const workDir = getWorkDir(repo)
  return [
    {
      action: 'Flash Fetch',
      command: 'flash fetch',
      workDir
    },
    {
      action: 'Flash Build',
      command: 'flash build',
      workDir
    }
  ]
}
const createUpdateOneCommands = (repo: string, storyId: string | undefined): JobCommand[] => {
  if (!storyId) throw new Error('Story ID is required for UPDATE_ONE job type')
  const workDir = getWorkDir(repo)

  return [
    {
      action: 'Flash Fetch Story',
      command: `flash fetch --entry-id=${storyId}`,
      workDir
    },
    {
      action: 'Flash Build Story',
      command: `flash build --entry-id=${storyId}`,
      workDir
    }
  ]
}

// Command map
export const JOB_COMMAND_MAP = {
  DEPLOY: createDeployCommands,
  UPDATE: createUpdateCommands,
  UPDATE_ONE: createUpdateOneCommands
} as const

// Build flag management
export async function removeBuildFlag(repo: string): Promise<void> {
  try {
    await unlinkSync(getBuildFlag(repo))
  } catch (error) {
    // Ignore if file doesn't exist
    if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
      throw error
    }
  }
}

export async function createBuildFlag(repo: string): Promise<void> {
  await writeFileSync(getBuildFlag(repo), new Date().toISOString())
}
