import { Worker } from 'bullmq'
import { Redis } from 'ioredis'

import { setJobCompleted } from '../db.js'
import type { JobDb } from '../types.js'
import { processJob } from './processor.js'

const redis = new Redis({ host: 'localhost', port: 6379 })

const connection = {
  host: '127.0.0.1',
  lockDuration: 300000, // 5 minutes lock
  port: 6379,
  stalledInterval: 300000 // Check for stalled jobs every 5 minutes
}

const QUEUE_NAMES = {
  CONCURRENT: 'concurrent',
  SEQUENTIAL: 'sequential'
} as const

const workers = {
  concurrent: new Worker<JobDb>(QUEUE_NAMES.CONCURRENT, processJob, {
    concurrency: 50,
    connection,
    lockDuration: connection.lockDuration,
    stalledInterval: connection.stalledInterval
  }),
  sequential: new Worker<JobDb>(QUEUE_NAMES.SEQUENTIAL, processJob, {
    concurrency: 1,
    connection,
    lockDuration: connection.lockDuration,
    stalledInterval: connection.stalledInterval
  })
}

// Enhanced logging for both workers
const setupWorkerLogging = (worker: Worker, name: string) => {
  worker.on('completed', (job) => {
    console.info(`[${name}] Job completed`, { job })
  })

  worker.on('failed', (job, err) => {
    console.error(`[${name}] Job failed`, { err, job })
    if (job?.data?.jobId) {
      setJobCompleted(job.data.jobId, 'error', JSON.stringify(err) || 'Unknown error')
    }
  })

  worker.on('stalled', (jobId) => {
    console.warn(`[${name}] Job stalled`, { jobId })
  })

  worker.on('error', (err) => {
    console.error(`[${name}] Worker error`, {
      err
    })
  })

  worker.on('active', (job) => {
    console.info(`[${name}] Job started`, {
      job
    })
  })
}

setupWorkerLogging(workers.concurrent, 'Concurrent')
setupWorkerLogging(workers.sequential, 'Sequential')

// Keep the process running
process.on('SIGTERM', async () => {
  await Promise.all([workers.concurrent.close(), workers.sequential.close(), redis.quit()])
})
