import type { FastifyReply, FastifyRequest } from 'fastify'

import { CONFIG } from '../config.js'

export async function validateRepository(
  request: FastifyRequest<{ Params: { repo: string } }>,
  reply: FastifyReply
): Promise<void> {
  const { repo } = request.params

  if (!CONFIG.repositories.has(repo)) {
    await reply.code(401).send({
      error: 'Unauthorized',
      message: 'Repository is not whitelisted'
    })
  }
}
