import { createBullBoard } from '@bull-board/api'
import { BullAdapter } from '@bull-board/api/bullAdapter.js'
import { FastifyAdapter } from '@bull-board/fastify'
import cors from '@fastify/cors'
import fastify from 'fastify'
import { nanoid } from 'nanoid'

import { CONFIG, hasDeployment } from './config.js'
import { createJob } from './db.js'
import { queues } from './queues/index.js'
import { jobRoutes } from './routes/jobs.js'
import { statusRoutes } from './routes/status.js'
import { log } from './utils/logger.js'

const server = fastify({
  logger: {
    level: 'warn',
    serializers: {
      req: (req) => ({
        method: req.method,
        url: req.url
      }),
      res: (res) => ({
        statusCode: res.statusCode
      })
    }
  },
  // Disable body parsing since we don't use request bodies
  disableRequestLogging: false,
  ignoreTrailingSlash: true
})

server.register(cors, {
  allowedHeaders: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  origin: '*'
})

// Disable body parsing for all routes since we don't use request bodies
server.addContentTypeParser('*', { parseAs: 'string' }, (_req, _body, done) => {
  done(null, undefined)
})

// Setup Bull Board
const serverAdapter = new FastifyAdapter()
createBullBoard({
  queues: [new BullAdapter(queues.concurrent), new BullAdapter(queues.sequential)],
  serverAdapter
})

serverAdapter.setBasePath('/queues')
server.register(serverAdapter.registerPlugin(), {
  basePath: '/queues',
  prefix: '/queues'
})

// Register status routes
server.register(statusRoutes)

// Register job routes
server.register(jobRoutes)

// Catch-all handler for unmatched routes - return 401
server.setNotFoundHandler(async (_request, reply) => {
  await reply.status(401).send({
    error: 'Unauthorized',
    message: 'Endpoint not allowed'
  })
})

const pushInitialDeployJobs = async () => {
  try {
    for (const repo of CONFIG.repositories) {
      if (!(await hasDeployment(repo))) {
        const jobId = nanoid()

        const job = createJob({
          jobId,
          repo,
          triggerDetails: { body: { message: 'Initial deploy on boot' }, headers: {} },
          type: 'DEPLOY'
        })
        await queues.sequential.add('DEPLOY ' + repo, job, { jobId })
        log('info', `Pushed initial deploy job for ${repo}`)
      } else {
        log('info', `Skipping initial deploy for ${repo} - deployment exists`)
      }
    }
  } catch (error) {
    log('error', 'Failed to push initial deploy jobs', { error })
  }
}

const start = async () => {
  try {
    await server.listen({ host: '0.0.0.0', port: 3000 })
    log('info', 'Server started successfully')
    await pushInitialDeployJobs()
  } catch (err) {
    log('error', 'Server failed to start', { error: err })
    process.exit(1)
  }
}

start()
