type JobType = 'DEPLOY' | 'UPDATE' | 'UPDATE_ONE'
type JobStatus = 'queued' | 'running' | 'completed' | 'error'

export interface JobCommand {
  action: string
  command: string
  workDir?: string
}

export interface TriggerDetails {
  headers: Record<string, string | string[] | undefined>
  body: unknown
}

export interface JobDb {
  id: number
  jobId: string
  type: JobType
  repo: string
  storyId?: string
  status: JobStatus
  result?: string
  createdAt: number
  startedAt?: number
  completedAt?: number
  triggerDetails: string
}

export interface CreateJobParams {
  jobId: string
  type: JobType
  repo: string
  storyId?: string
  triggerDetails: TriggerDetails
}

// Request/Response types for each endpoint
export interface DeployRequest {
  Params: {
    repo: string
  }
  Body: Record<string, unknown>
  Headers: Record<string, string | string[] | undefined>
}

export interface UpdateRequest {
  Params: {
    repo: string
  }
  Body: Record<string, unknown>
  Headers: Record<string, string | string[] | undefined>
}

export interface UpdateStoryRequest {
  Params: {
    repo: string
    storyId: string
  }
  Body: Record<string, unknown>
  Headers: Record<string, string | string[] | undefined>
}

export interface JobResponse {
  job: JobDb
}

export interface JobsListResponse {
  jobs: JobDb[]
}
