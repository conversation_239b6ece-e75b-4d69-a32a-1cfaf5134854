import type { Database as DatabaseType } from 'better-sqlite3'
import Database from 'better-sqlite3'

import type { CreateJobParams, JobDb } from './types.js'

export const db: DatabaseType = new Database('./db/flash-preview.db')

// Initialize the database
db.exec(`
  CREATE TABLE IF NOT EXISTS jobs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    jobId TEXT NOT NULL,
    type TEXT NOT NULL,
    repo TEXT NOT NULL,
    storyId TEXT,
    status TEXT NOT NULL,
    result TEXT,
    triggerDetails TEXT NOT NULL,
    createdAt INTEGER NOT NULL,
    startedAt INTEGER,
    completedAt INTEGER,
    CHECK (type IN ('DEPLOY', 'UPDATE', 'UPDATE_ONE')),
    CHECK (status IN ('queued', 'running', 'completed', 'error'))
  )
`)

// Prepare statements
const createJobStmt = db.prepare(`
  INSERT INTO jobs (jobId, type, repo, storyId, triggerDetails, status, createdAt)
  VALUES (?, ?, ?, ?, ?, 'queued', ?)
  RETURNING *
`)

const getJobsStmt = db.prepare(`
  SELECT * FROM jobs ORDER BY createdAt DESC LIMIT 100
`)

const getRepoJobsStmt = db.prepare(`
  SELECT * FROM jobs WHERE repo = ? ORDER BY createdAt DESC LIMIT 100
`)

const setJobRunningStmt = db.prepare(`
  UPDATE jobs SET status = 'running', startedAt = ? WHERE jobId = ?
`)

const setJobCompletedStmt = db.prepare(`
  UPDATE jobs SET status = ?, result = ?, completedAt = ? WHERE jobId = ?
`)

// Exported functions
export function createJob(params: CreateJobParams): JobDb {
  const { jobId, type, repo, storyId, triggerDetails } = params
  return createJobStmt.get(jobId, type, repo, storyId || null, JSON.stringify(triggerDetails), Date.now()) as JobDb
}

export function getJobs(): JobDb[] {
  return getJobsStmt.all() as JobDb[]
}

export function getRepoJobs(repo: string): JobDb[] {
  return getRepoJobsStmt.all(repo) as JobDb[]
}

export function setJobRunning(jobId: string): void {
  setJobRunningStmt.run(Date.now(), jobId)
}

export function setJobCompleted(jobId: string, status: 'completed' | 'error', result: string): void {
  setJobCompletedStmt.run(status, result, Date.now(), jobId)
}

const getJobStmt = db.prepare(`
  SELECT * FROM jobs WHERE jobId = ?
`)

export function getJob(jobId: string): JobDb | undefined {
  try {
    return getJobStmt.get(jobId) as JobDb | undefined
  } catch (e) {
    return undefined
  }
}
