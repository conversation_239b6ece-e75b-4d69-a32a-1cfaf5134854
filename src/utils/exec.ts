import type { ExecSyncOptions } from 'child_process'
import { execSync } from 'child_process'

import { log } from './logger.js'

export function execSyncWithErrorHandling(
  command: string,
  action: string,
  repo: string,
  jobType: string,
  jobId: string,
  options: ExecSyncOptions = {}
): string {
  const startTime = Date.now()
  try {
    log('info', `${action} started`, { jobId, repo, type: jobType })

    const result = execSync(command, {
      encoding: 'utf8',
      ...options
    })

    const duration = (Date.now() - startTime) / 1000

    const resultString = result.toString()
    if (resultString.includes('Killed')) {
      throw new Error(`Node process was killed. Resource limit exceeded probably: ${resultString}`)
    }

    log('info', `${action} completed`, { duration, jobId, repo, type: jobType })
    return `${action}: ${resultString}\nCompleted in ${duration} seconds\n`
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000

    log('error', `${action} failed`, {
      duration,
      error,
      jobId,
      repo,
      type: jobType
    })

    const errorMessage =
      error && typeof error === 'object' && 'message' in error ? error.message : JSON.stringify(error)

    throw new Error(`${action} failed after ${duration} seconds: ${errorMessage}`)
  }
}
