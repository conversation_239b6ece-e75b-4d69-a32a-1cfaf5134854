type LogLevel = 'info' | 'error' | 'debug'

interface LogMetadata {
  jobId?: string
  repo?: string
  type?: string
  duration?: number
  details?: string
  error?: unknown
}

export function log(level: LogLevel, message: string, metadata: LogMetadata = {}) {
  const timestamp = new Date().toISOString()
  const { jobId, repo, type, duration, error } = metadata

  let logMessage = `[${timestamp}]`
  if (jobId) logMessage += ` [Job ${jobId}]`
  if (repo) logMessage += ` [${repo}]`
  if (type) logMessage += ` [${type}]`

  logMessage += ` ${message}`

  if (duration !== undefined) {
    logMessage += ` (${duration.toFixed(2)}s)`
  }

  if (error) {
    logMessage += `\nError: ${JSON.stringify(error, null, 2)}`
  }

  switch (level) {
    case 'error':
      console.error(logMessage)
      break
    case 'debug':
      console.debug(logMessage)
      break
    default:
      console.info(logMessage)
  }
}
