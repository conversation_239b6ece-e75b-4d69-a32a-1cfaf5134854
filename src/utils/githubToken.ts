import axios, { AxiosError } from 'axios'
import jwt from 'jsonwebtoken'

import { CONFIG } from '../config.js'
import { log } from './logger.js'

interface GitHubTokenResponse {
  token: string
  expires_at: string
}

export async function getInstallationToken(): Promise<string> {
  const startTime = Date.now()

  try {
    if (!CONFIG.GH_APP_ID || !CONFIG.GH_PRIVATE_KEY || !CONFIG.GH_APP_INSTALLATION_ID) {
      throw new Error('Missing required GitHub App credentials')
    }

    const now = Math.floor(Date.now() / 1000)
    const payload = { exp: now + 600, iat: now, iss: CONFIG.GH_APP_ID }
    const privateKey = CONFIG.GH_PRIVATE_KEY.replace(/\\n/g, '\n')

    let jwtToken: string
    try {
      jwtToken = jwt.sign(payload, privateKey, { algorithm: 'RS256' })
    } catch (error) {
      log('error', 'Failed to sign JWT token', { error, type: 'jwt_signing' })
      throw new Error('JWT signing failed')
    }

    const url = `https://api.github.com/app/installations/${CONFIG.GH_APP_INSTALLATION_ID}/access_tokens`

    const response = await axios.post<GitHubTokenResponse>(
      url,
      {},
      {
        headers: {
          Accept: 'application/vnd.github+json',
          Authorization: `Bearer ${jwtToken}`
        }
      }
    )

    const duration = (Date.now() - startTime) / 1000
    log('info', 'Successfully fetched GitHub installation token', {
      details: `Token expires at ${response.data.expires_at}`,
      duration,
      type: 'github_token'
    })

    return response.data.token
  } catch (error) {
    const duration = (Date.now() - startTime) / 1000

    if (error instanceof AxiosError) {
      log('error', 'GitHub API request failed', {
        duration,
        error: {
          data: error.response?.data,
          message: error.message,
          status: error.response?.status
        },
        type: 'github_token'
      })
      throw new Error(`GitHub API request failed: ${error.message}`)
    }

    log('error', 'Failed to get GitHub installation token', {
      duration,
      error,
      type: 'github_token'
    })
    throw error
  }
}
