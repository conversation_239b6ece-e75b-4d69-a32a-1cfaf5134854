import fs from 'node:fs/promises'

import { config } from 'dotenv'
config()

const validateEnv = () => {
  const required = ['GH_APP_ID', 'GH_APP_INSTALLATION_ID', 'GH_PRIVATE_KEY'] as const

  const missing = required.filter((key) => !process.env[key])

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

validateEnv()

const testRepositoriesArray = ['mercy', 'ucdpa']

const productionRepositoriesArray = [
  'mercy',
  'ucdpa',
  'codema-dcc',
  'swoofee',
  '3dental',
  'adult-literacy',
  'cafico',
  'comsec2020',
  'dbass',
  'dilosk',
  'biosphere',
  'dublin-hearing-aid-centre',
  'dynamaviation',
  'extra-space',
  'gordon',
  'acoustic-technologies',
  'itc-international',
  'kajjal',
  'kinara2020',
  'kinarakitchen',
  'know-your-flow',
  'loveclontarf',
  'marketing-clever',
  'masterfire',
  'hjl',
  'pieta',
  'pinnaclepartners',
  'qsn',
  'safer-phosphates',
  'santiago',
  'santos-dumont',
  'solas',
  'stewarts-care',
  'storm',
  'technopath',
  'thisisfet',
  'whelanlaw',
  'wobh',
  'yieldhub'
]

const repositories = process.env.TEST_MODE ? testRepositoriesArray : productionRepositoriesArray

export const CONFIG = {
  // Constants
  BUILD_FLAG: '.BUILD_OK',
  DOMAIN: process.env.DOMAIN || 'preview.server',
  GH_APP_ID: process.env.GH_APP_ID,
  GH_APP_INSTALLATION_ID: process.env.GH_APP_INSTALLATION_ID,
  GH_ORG: process.env.GH_ORG || 'Together-Digital',
  GH_PRIVATE_KEY: process.env.GH_PRIVATE_KEY || '',
  TEST_MODE: process.env.TEST_MODE,
  repositories: new Set(repositories)
} as const

export const getWorkDir = (repo: string): string => `sites/${repo}`
export const getBuildFlag = (repo: string): string => `${getWorkDir(repo)}/${CONFIG.BUILD_FLAG}`
const getOutDir = (repo: string): string => `${getWorkDir(repo)}/.BUILD_OK`
export const hasDeployment = async (repo: string): Promise<boolean> => {
  try {
    const outDir = getOutDir(repo)
    await fs.access(outDir)
    return true
  } catch {
    return false
  }
}
