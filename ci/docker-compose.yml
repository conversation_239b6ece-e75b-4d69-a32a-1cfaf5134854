services:
  app:
    image: flash-preview-server
    build:
      context: ..
      dockerfile: ci/Dockerfile
      platforms:
        - linux/arm64
    container_name: flash-preview-server
    env_file:
      - ../.env
    mem_limit: 4g
    user: root
    volumes:
      - /tmp/flash-preview/logs:/var/log:rw,z
      - /tmp/flash-preview/nginx-tmp:/var/lib/nginx/tmp:rw,z
      - /tmp/flash-preview/npm:/root/.npm:rw,z
      - /tmp/flash-preview/cache:/root/.cache:rw,z
      - /tmp/flash-preview/app/sites:/root/app/sites:rw,z
      - /tmp/flash-preview/app/db:/root/app/db:rw,z
    ports:
      - "80:80"
    restart: unless-stopped