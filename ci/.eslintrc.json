{"root": true, "env": {"browser": true, "es2022": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json", "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "import", "jsonc", "simple-import-sort", "sort-keys-fix", "unused-imports", "@stylistic"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["error", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "sort-keys-fix/sort-keys-fix": "warn", "@typescript-eslint/no-unused-vars": "off"}, "overrides": [{"files": ["*.json", "*.jsonc"], "parser": "jsonc-eslint-parser", "plugins": ["jsonc"], "extends": ["plugin:jsonc/recommended-with-jsonc"], "rules": {"jsonc/indent": ["error", 2], "jsonc/key-spacing": ["error", {"beforeColon": false, "afterColon": true}]}}]}