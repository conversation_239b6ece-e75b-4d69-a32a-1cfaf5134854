# Input variables
variable "project_name" {
  description = "A name for the whole project"
  type        = string
  validation {
    condition     = length(var.project_name) > 0
    error_message = "The project name must not be empty"
  }
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "The project name must only contain lowercase letters, numbers, and hyphens"
  }
}

variable "region" {
  description = "The region to deploy resources in"
  type        = string
  default     = "europe-west2"
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.region))
    error_message = "The region must only contain lowercase letters, numbers, and hyphens"
  }
}

variable "org_id" {
  description = "The organization ID"
  type        = string
  default     = "*************"
  validation {
    condition     = length(var.org_id) > 0
    error_message = "The organization ID must not be empty"
  }
  validation {
    condition     = can(regex("^[0-9]{4,}$", var.org_id))
    error_message = "The organization ID must be a number with at least four digits"
  }
}

variable "billing_account" {
  default     = "018446-E1C3B9-E4E116"
  description = "The billing account ID"
  type        = string
}

variable "gh_app_id" {
  description = "GitHub App ID"
  type        = string
}

variable "gh_app_installation_id" {
  description = "GitHub App Installation ID"
  type        = string
}

variable "gh_private_key" {
  description = "GitHub App Private Key"
  type        = string
  sensitive   = true
}

variable "domain" {
  description = "Server domain"
  type        = string
}

variable "basic_auth_user" {
  description = "Basic auth username"
  type        = string
  sensitive   = true
}

variable "basic_auth_pass" {
  description = "Basic auth password"
  type        = string
  sensitive   = true
}

variable "repositories" {
  description = "Comma-separated list of repository names"
  type        = string
  default     = "mercy,pieta,ucdpa,whelanlaw,codema-dcc,swoofee,3dental,adult-literacy,cafico,comsec2020,dbass,dilosk,biosphere,dublin-hearing-aid-centre,dynamaviation,extra-space,gordon,acoustic-technologies,itc-international,kajjal,kinara2020,kinarakitchen,know-your-flow,loveclontarf,marketing-clever,masterfire,hjl,pinnaclepartners,qsn,safer-phosphates,santiago,santos-dumont,solas,stewarts-care,storm,technopath,thisisfet,wobh,yieldhub"
}

# Project Settings
provider "google-beta" {
  region = var.region
}

resource "random_integer" "project_id" {
  min = 1000
  max = 9999
}

locals {
  project_id = "${var.project_name}-${random_integer.project_id.result}"
  repo_list  = [for repo in split(",", var.repositories) : trim(repo, " ")]
  domains = concat(
    [var.domain],
    [for repo in local.repo_list : "${repo}.${var.domain}"]
  )
}

resource "google_project" "project" {
  name                = var.project_name
  project_id          = local.project_id
  org_id              = var.org_id
  billing_account     = var.billing_account
  auto_create_network = false

  lifecycle {
    prevent_destroy = false
  }
}

module "project_services" {
  source                      = "terraform-google-modules/project-factory/google//modules/project_services"
  disable_services_on_destroy = true
  project_id                  = google_project.project.project_id
  enable_apis                 = true

  activate_apis = [
    "compute.googleapis.com",
    "iam.googleapis.com",
    "artifactregistry.googleapis.com",
    "servicenetworking.googleapis.com",
    "osconfig.googleapis.com",
  "cloudresourcemanager.googleapis.com"]

}

# Container Registry

resource "google_artifact_registry_repository" "repository" {
  location      = var.region
  project       = google_project.project.project_id
  repository_id = var.project_name
  format        = "DOCKER"

  docker_config {
    immutable_tags = false

  }


  cleanup_policies {
    id     = "keep-latest-only"
    action = "KEEP"
    most_recent_versions {
      keep_count = 1
    }
  }

  depends_on = [module.project_services]
}
# Add IAM policy binding to make the repository public
resource "google_artifact_registry_repository_iam_member" "public_reader" {
  provider   = google-beta
  project    = google_project.project.project_id
  location   = google_artifact_registry_repository.repository.location
  repository = google_artifact_registry_repository.repository.name
  role       = "roles/artifactregistry.reader"
  member     = "allUsers"
}
# =====================================================================
# Service Account for GitHub Actions
# =====================================================================
resource "google_service_account" "github_actions" {
  project      = google_project.project.project_id
  account_id   = "gh-actions"
  display_name = "Service Account for GitHub Actions"
  depends_on   = [google_project.project]
}

resource "google_project_iam_member" "github_actions_compute_admin" {
  project = google_project.project.project_id
  role    = "roles/compute.instanceAdmin.v1"
  member  = "serviceAccount:${google_service_account.github_actions.email}"
}

resource "google_service_account_key" "github_actions_key" {
  service_account_id = google_service_account.github_actions.name
}

resource "google_service_account_iam_member" "github_actions_service_account_user" {
  service_account_id = google_service_account.compute_sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${google_service_account.github_actions.email}"
}

# =====================================================================
# IAM Configuration
# =====================================================================
resource "google_artifact_registry_repository_iam_member" "github_actions_writer" {
  provider   = google-beta
  project    = google_project.project.project_id
  location   = google_artifact_registry_repository.repository.location
  repository = google_artifact_registry_repository.repository.name
  role       = "roles/artifactregistry.writer"
  member     = "serviceAccount:${google_service_account.github_actions.email}"
}


# Service Account
locals {
  compute_roles = [
    "roles/logging.logWriter",
    "roles/artifactregistry.reader"
  ]
}

resource "google_service_account" "compute_sa" {
  project      = google_project.project.project_id
  account_id   = "compute-sa"
  display_name = "Service Account for Compute Engine"
  depends_on   = [google_project.project]
}

resource "google_project_iam_member" "compute_roles" {
  for_each = toset(local.compute_roles)
  project  = google_project.project.project_id
  role     = each.key
  member   = "serviceAccount:${google_service_account.compute_sa.email}"
}

# Networking

resource "google_compute_network" "default" {
  project                 = google_project.project.project_id
  name                    = "default"
  auto_create_subnetworks = false
}

resource "google_compute_subnetwork" "default" {
  project                  = google_project.project.project_id
  name                     = "default"
  region                   = var.region
  network                  = google_compute_network.default.name
  ip_cidr_range            = "***********/16"
  private_ip_google_access = true
}

# Load Balancer

resource "google_compute_global_address" "default" {
  project = google_project.project.project_id
  name    = "${var.project_name}-lb-ip"
}

resource "google_compute_managed_ssl_certificate" "default" {
  project = google_project.project.project_id
  name    = "${var.project_name}-ssl-cert"

  managed {
    domains = local.domains
  }
}


resource "google_compute_instance_group" "default" {
  project   = google_project.project.project_id
  name      = "${var.project_name}-instance-group"
  zone      = google_compute_instance.app_instance.zone
  instances = [google_compute_instance.app_instance.id]

  named_port {
    name = "http"
    port = 80
  }
}

resource "google_compute_backend_service" "default" {

  project     = google_project.project.project_id
  name        = "${var.project_name}-backend"
  protocol    = "HTTP"
  port_name   = "http"
  timeout_sec = 60

  health_checks = [google_compute_health_check.default.id]
  enable_cdn    = false

  backend {
    group           = google_compute_instance_group.default.id
    max_utilization = 1.0
  }
}

resource "google_compute_url_map" "default" {

  project = google_project.project.project_id
  name    = "${var.project_name}-lb-url-map"

  default_service = google_compute_backend_service.default.id
}

resource "google_compute_target_https_proxy" "default" {

  project          = google_project.project.project_id
  name             = "${var.project_name}-https-proxy"
  url_map          = google_compute_url_map.default.self_link
  ssl_certificates = [google_compute_managed_ssl_certificate.default.self_link]
}

resource "google_compute_global_forwarding_rule" "default" {

  project    = google_project.project.project_id
  name       = "${var.project_name}-lb-forwarding-rule"
  target     = google_compute_target_https_proxy.default.self_link
  port_range = 443
  ip_address = google_compute_global_address.default.address
}

resource "google_compute_health_check" "default" {
  project = google_project.project.project_id
  name    = "${var.project_name}-health-check"

  check_interval_sec  = 60
  timeout_sec         = 1
  healthy_threshold   = 1
  unhealthy_threshold = 1


  http_health_check {
    port         = 80
    request_path = "/health"
  }
}

# Firewall Rules

resource "google_compute_firewall" "allow_http" {
  project = google_project.project.project_id
  name    = "allow-http"
  network = google_compute_network.default.name

  allow {
    protocol = "tcp"
    ports    = [80]
  }

  source_ranges = ["0.0.0.0/0"]
}

resource "google_compute_firewall" "allow_ssh" {
  project = google_project.project.project_id
  name    = "allow-ssh"
  network = google_compute_network.default.name

  allow {
    protocol = "tcp"
    ports    = ["22"]
  }

  source_ranges = ["0.0.0.0/0"]
}



module "gce-container" {
  source  = "terraform-google-modules/container-vm/google"
  version = "~> 3.2"


  container = {
    image = "europe-west2-docker.pkg.dev/flash-2024-builder-8001/flash-2024-builder/flash-2024-builder:latest"
    env = [
      { name = "NODE_ENV", value = "development" },
      { name = "DEBUG", value = "1" },
      { name = "GH_APP_ID", value = var.gh_app_id },
      { name = "GH_APP_INSTALLATION_ID", value = var.gh_app_installation_id },
      { name = "GH_PRIVATE_KEY", value = var.gh_private_key },
      { name = "DOMAIN", value = var.domain },
      { name = "BASIC_AUTH_USER", value = var.basic_auth_user },
      { name = "BASIC_AUTH_PASS", value = var.basic_auth_pass },
      { name = "npm_config_cache", value = "/root/.npm" }
    ]
    ports = [
      { containerPort = 80 },
    ]
    volumeMounts = [
      { name = "flash-preview-tmp", mountPath = "/tmp" },
      { name = "flash-preview-logs", mountPath = "/var/log" },
      { name = "flash-preview-nginx-tmp", mountPath = "/var/lib/nginx/tmp" },
      { name = "flash-preview-npm", mountPath = "/root/.npm" },
      { name = "flash-preview-cache", mountPath = "/root/.cache" },
      { name = "flash-preview-sites", mountPath = "/root/app/sites" },
    { name = "flash-preview-db", mountPath = "/root/app/db" }]
  }
  volumes = [
    { name = "flash-preview-tmp", hostPath = { path = "/mnt/disks/flash-preview/tmp" } },
    { name = "flash-preview-logs", hostPath = { path = "/mnt/disks/flash-preview/logs" } },
    { name = "flash-preview-nginx-tmp", hostPath = { path = "/mnt/disks/flash-preview/nginx-tmp" } },
    { name = "flash-preview-npm", hostPath = { path = "/mnt/disks/flash-preview/npm" } },
    { name = "flash-preview-cache", hostPath = { path = "/mnt/disks/flash-preview/cache" } },
    { name = "flash-preview-sites", hostPath = { path = "/mnt/disks/flash-preview/sites" } },
    { name = "flash-preview-db", hostPath = { path = "/mnt/disks/flash-preview/db" } }
  ]
  restart_policy = "Always"

}


# Persistent Disk for flash-preview data
resource "google_compute_disk" "flash_preview_disk" {
  project = google_project.project.project_id
  name    = "${var.project_name}-flash-preview-disk"
  type    = "pd-ssd"
  zone    = "${var.region}-a"
  size    = 40 # Size in GB
}

# Compute Engine

resource "google_compute_instance" "app_instance" {
  project      = google_project.project.project_id
  name         = "${var.project_name}-instance"
  machine_type = "e2-medium"
  zone         = "${var.region}-a"

  boot_disk {
    initialize_params {
      image = "cos-cloud/cos-stable"
    }
  }

  attached_disk {
    source      = google_compute_disk.flash_preview_disk.self_link
    device_name = "flash-preview-disk"
  }

  network_interface {
    network    = google_compute_network.default.self_link
    subnetwork = google_compute_subnetwork.default.self_link

    access_config {
      // Ephemeral public IP
    }
  }

  metadata = {
    "enable-osconfig"         = "true"
    gce-container-declaration = module.gce-container.metadata_value
    startup-script            = <<-EOF
      #!/bin/bash
      # Format the disk if it's not already formatted
      if ! blkid /dev/disk/by-id/google-flash-preview-disk; then
        mkfs.ext4 -m 0 -E lazy_itable_init=0,lazy_journal_init=0,discard /dev/disk/by-id/google-flash-preview-disk
      fi

      # Create mount point
      mkdir -p /mnt/disks/flash-preview

      # Mount the disk
      mount -o discard,defaults /dev/disk/by-id/google-flash-preview-disk /mnt/disks/flash-preview

      # Add to /etc/fstab to mount on reboot
      if ! grep -q "/dev/disk/by-id/google-flash-preview-disk" /etc/fstab; then
        echo "/dev/disk/by-id/google-flash-preview-disk /mnt/disks/flash-preview ext4 discard,defaults 0 2" >> /etc/fstab
      fi

      # Create and set permissions for flash-preview directory
      mkdir -p /mnt/disks/tmp/flash-preview
      chmod 777 /mnt/disks/tmp/flash-preview

      # Clean up docker
      docker system prune -af
    EOF

    force_redeploy = timestamp()

    google-logging-enabled    = "true"
    google-monitoring-enabled = "true"
  }

  service_account {
    email  = google_service_account.compute_sa.email
    scopes = ["cloud-platform"]
  }

  allow_stopping_for_update = true
  depends_on = [
    google_artifact_registry_repository.repository
  ]
}

# Output

locals {
  container_repository_url = "${var.region}-docker.pkg.dev/${google_project.project.project_id}/${var.project_name}"
}

# Update your outputs
output "repository_url" {
  value       = local.container_repository_url
  description = "The URL of the Artifact Registry repository"
}

output "credentials_json" {
  value       = google_service_account_key.github_actions_key.private_key
  description = "Service account JSON key"
  sensitive   = true
}



output "public_url" {
  value       = "https://${var.domain}"
  description = "The public URL of the application"
}

output "github_actions_setup" {
  value = <<EOT
# GitHub Actions Variables Setup

Add these repository variables:
GCP_REPOSITORY_URL: ${local.container_repository_url}

Add this repository secret:
GCP_CREDENTIALS: <The credentials_json output value (service account key)>

EOT
}

output "dns_records" {
  value       = <<EOT
# AWS Route53 DNS Records Setup

Create the following A records:
1. ${var.domain} -> ${google_compute_global_address.default.address}
2. *.${var.domain} -> ${google_compute_global_address.default.address}

EOT
  description = "DNS records that need to be created in AWS Route53"
}
