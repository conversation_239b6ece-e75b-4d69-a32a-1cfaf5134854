<!doctype html>
<html>
  <head>
    <title>Updating website...</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        background-color: #f5f5f5;
      }
      .container {
        text-align: center;
        padding: 20px;
      }
      h1 {
        color: #333;
      }
      .error-message {
        color: #dc3545;
        margin-top: 20px;
        display: none;
      }
    </style>
    <script>
      async function checkJobStatus() {
        try {
          const response = await fetch(`https://flash-preview.togetherdigital.ie/jobs/${jobId}`)

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          const jsonResponse = await response.json()

          console.log({ jsonResponse })

          const { status } = jsonResponse.job

          if (status === 'completed' && sourcePath) {
            clearInterval(pollInterval)
            window.location.href = sourcePath
          } else if (status === 'error') {
            clearInterval(pollInterval)
            document.getElementById('error-message').style.display = 'block'
          }
        } catch (error) {
          clearInterval(pollInterval)
          document.getElementById('error-message').style.display = 'block'
          console.error('Error checking job status:', error)
        }
      }
      const urlParams = new URLSearchParams(window.location.search)
      const sourcePath = urlParams.get('sourcePath')
      const jobId = urlParams.get('jobId')
      let pollInterval

      console.log({ sourcePath, jobId })

      if (!sourcePath) {
        console.error('No sourcePath provided')
        window.location.href = '/404.html'
      }

      if (!jobId) {
        // Redirect to source path if no job ID after 500 ms
        console.warn('No jobId provided')
        setTimeout(() => {
          window.location.href = sourcePath
        }, 500)
      }

      if (jobId && sourcePath) {
        // Start polling every 1 second
        pollInterval = setInterval(checkJobStatus, 1000)
        // Also check immediately
        checkJobStatus()
      }
    </script>
  </head>
  <body>
    <div class="container">
      <h1>Updating website...</h1>
      <div id="error-message" class="error-message">There was an error updating the website.</div>
    </div>
  </body>
</html>
