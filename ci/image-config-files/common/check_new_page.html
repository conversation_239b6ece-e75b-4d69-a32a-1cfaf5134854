<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loading</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        background-color: #f5f5f5;
      }
      .container {
        text-align: center;
        padding: 20px;
      }
      h1 {
        color: #333;
        font-size: 48px;
        margin-bottom: 20px;
      }
      p {
        color: #666;
        font-size: 18px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Loading</h1>
      <p id="message">Loading</p>
    </div>

    <script>
      function getQueryParam(param) {
        const urlParams = new URLSearchParams(window.location.search)
        return urlParams.get(param)
      }

      const updatePreview = async (storyblokId, repo) => {
        const messageEl = document.getElementById('message')

        try {
          const response = await fetch(`https://flash-preview.togetherdigital.ie/update/${repo}/story/${storyblokId}`, {
            method: 'POST'
          })

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          const data = await response.json()
          const { jobId } = data.job

          console.log({ data })

          // Redirect to is_building.html with sourcePath and jobId
          const currentPath = window.location.pathname
          const redirectUrl = new URL('/is_building.html', window.location.origin)
          redirectUrl.searchParams.set('sourcePath', sourcePath)
          redirectUrl.searchParams.set('jobId', jobId)
          const redirectUrlString = redirectUrl.toString()
          console.log({ redirectUrlString })
          window.location.href = redirectUrlString
        } catch (error) {
          console.error('Failed to update preview:', error)
          messageEl.textContent = 'Failed to update preview. Please try again.'
        }
      }

      // Execute immediately
      const sourcePath = getQueryParam('sourcePath')
      const repo = getQueryParam('repo')

      // Extract _storyblok from sourcePath
      let storyblokId = null
      if (sourcePath) {
        const sourceUrl = new URL(sourcePath, window.location.origin)
        storyblokId = sourceUrl.searchParams.get('_storyblok')
      }

      console.log({ storyblokId, repo, sourcePath })

      if (!storyblokId || !repo || !sourcePath) {
        console.error('missing parameters')
        window.location.href = '/404.html'
      }

      if (storyblokId && repo && sourcePath) {
        updatePreview(storyblokId, repo, sourcePath)
      }
    </script>
  </body>
</html>
