# Map block to extract repository name from subdomain
map $host $repo {
    ~^(?<capture>[^.]+)\.${DOMAIN}$ $capture;
    default "";
}

# Add a variable to check if URI ends with a filename
map $uri $has_filename {
    ~\.[a-zA-Z0-9]+$ 1;
    default 0;
}

# Server block for repository sites
server {
    listen 80;
    server_name ~^(?<repo>[^.]+)\.${DOMAIN}$;
    access_log off;

    absolute_redirect off;

    root /root/app/sites/$repo/build;

    location = /is_building.html {
        alias /root/common/is_building.html;
    }

    location = /check_new_page.html {
        alias /root/common/check_new_page.html;
    }

    if (!-d /root/app/sites/$repo) {
      return 404;
     }
    

    set $is_building 0;
    set $check_new_page 0;

    if (!-f /root/app/sites/$repo/.BUILD_OK) {
        set $is_building 1;
    }
    
    if ($has_filename = 0) {
        set $is_building "${is_building}1";
        set $check_new_page 1;
    }

    if ($is_building = "11") {
        return 307 /is_building.html?sourcePath=$request_uri;
    }

    if (!-f /root/app/sites/$repo/build${uri}.html) {
        set $check_new_page "${check_new_page}1";
    }

    if (!-f /root/app/sites/$repo/build${uri}) {
        set $check_new_page "${check_new_page}1";
    }

    if (!-f /root/app/sites/$repo/build${uri}/index.html) {
        set $check_new_page "${check_new_page}1";
    }

    if (!-f /root/app/sites/$repo/build${uri}index.html) {
        set $check_new_page "${check_new_page}1";
    }

    if ($check_new_page = "11111") {
        return 307 /check_new_page.html?repo=$repo&sourcePath=$request_uri;
    }

    location / {
        limit_except GET HEAD OPTIONS {
            auth_basic ${BASIC_AUTH_ENABLED};
            auth_basic_user_file /etc/nginx/.htpasswd;
        }

        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;

        include /etc/nginx/snippets/cors.conf;

        try_files $uri $uri/ =404;
        
        autoindex on;
        autoindex_format html;
    }

    include /etc/nginx/snippets/common_locations.conf;
    include /etc/nginx/snippets/static_file_cache.conf;
}

# Server block for API on root domain
server {
    listen 80;
    server_name ${DOMAIN};

    include /etc/nginx/snippets/common_locations.conf;

    # Proxy all requests to Node.js API server
    location / {
        # Enable basic auth only for GET requests
        limit_except POST OPTIONS {
            auth_basic ${BASIC_AUTH_ENABLED};
            auth_basic_user_file /etc/nginx/.htpasswd;
        }

      add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0" always;
      add_header Pragma "no-cache" always;
      add_header Expires "0" always;

      add_header 'Content-Security-Policy' "frame-ancestors *";
      add_header 'X-Frame-Options' "ALLOWALL";

        location /health {
            auth_basic off;
            access_log off;
            add_header Content-Type text/plain;
            return 200 'ok';
        }

        location ~ ^/deploy/.* {
            limit_except POST OPTIONS {
                deny all;
            }
            auth_basic off;
            access_log off;
            proxy_pass http://127.0.0.1:3000;
            include /etc/nginx/snippets/proxy.conf;
        }

        location ~ ^/update/.* {
            limit_except POST OPTIONS {
                deny all;
            }
            auth_basic off;
            access_log off;
            proxy_pass http://127.0.0.1:3000;
            include /etc/nginx/snippets/proxy.conf;
        }

        proxy_pass http://127.0.0.1:3000;
        include /etc/nginx/snippets/proxy.conf;
    }

    # Serve log files with basic auth
    location /logs {
        alias /var/log;
        access_log off;
        autoindex on;
        autoindex_format html;
    }

     location /jobs {
        proxy_pass http://127.0.0.1:3000;
        include /etc/nginx/snippets/proxy.conf;
    }

    location /queues {
        proxy_pass http://127.0.0.1:3000;
        include /etc/nginx/snippets/proxy.conf;
    }
}

# Default server block - catch any unmatched requests
server {
    listen 80 default_server;
    server_name _;

    include /etc/nginx/snippets/common_locations.conf;
    include /etc/nginx/snippets/health.conf;

    # Return 401 for all other requests
    location / {
      limit_except OPTIONS {
        auth_basic ${BASIC_AUTH_ENABLED};
        auth_basic_user_file /etc/nginx/.htpasswd;
      }

      expires 1y;
      return 401;
    }
}
