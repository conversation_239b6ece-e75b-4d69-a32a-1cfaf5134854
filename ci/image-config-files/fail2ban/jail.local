[DEFAULT]
# Ban hosts for 1 hour (3600 seconds)
bantime = 3600

# A host is banned if it has generated "maxretry" during the last "findtime" seconds
findtime = 600

# Number of failures before a host gets banned
maxretry = 5

# Default action: ban only
action = iptables-multiport

# Enable logging
logtarget = /var/log/fail2ban.log

# Disable SSH jail (not needed in container)
[sshd]
enabled = false

[nginx-http-auth]
enabled = true
port = http,https
filter = nginx-http-auth
logpath = /var/log/nginx/access.log
maxretry = 5
bantime = 3600
findtime = 600
