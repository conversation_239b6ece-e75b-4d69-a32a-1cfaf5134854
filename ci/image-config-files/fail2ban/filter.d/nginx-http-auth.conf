# Fail2Ban filter for nginx HTTP basic auth failures
#
# This filter matches HTTP 401 responses in nginx access logs
# which indicate failed basic authentication attempts

[Definition]

# Match lines with HTTP 401 status code (authentication failed)
failregex = ^<HOST> -.*"(GET|POST|HEAD|PUT|DELETE|PATCH|OPTIONS).*" 401 .*$

# Ignore successful authentications (HTTP 200, 201, 204, etc.)
ignoreregex = ^<HOST> -.*"(GET|POST|HEAD|PUT|DELETE|PATCH|OPTIONS).*" (200|201|204|301|302|304) .*$
