# local env files
.env
.env.local
.env.production
.env.development
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln

dist/
out
node_modules/
ssrBuildsImages/
logs/
yarn.lock
.DS_Store
*cache*

.eslintcache

sites
.dumprdb

# Ignore all cloned sites
sites
node_modules


.env

**.env
id_ed25519

*.tfvars
# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log


# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc


*.DS_Config
*.DS_Store
package-lock.json
*.md
*.yml