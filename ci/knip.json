{"$schema": "https://unpkg.com/knip@2/schema.json", "commitlint": {"config": ["ci/.commitlintrc.json"]}, "entry": ["src/index.ts", "src/queues/worker.ts"], "eslint": {"config": ["ci/.eslintrc.json"], "entry": "ci/.eslintrc.json"}, "ignore": ["node_modules", "dist", "coverage", "tmp", "tmp-*", "tmp.*", ".*", ".*.*", ".*-*", ".*.log", ".*.log.*"], "ignoreBinaries": ["docker-compose", "redis-server"], "prettier": {"config": ["ci/.prettierrc.json"]}, "project": ["src/**/*.ts", "ci/**/*.ts", "src/index.ts", "test/**/*.ts", "ci/*.ts", "src/*.ts"]}