{"dependencies": {"@bull-board/api": "^4.10.1", "@bull-board/fastify": "^4.10.1", "@fastify/cors": "^9.0.0", "axios": "^1.0.0", "better-sqlite3": "11.5.0", "bullmq": "^5.21.2", "dotenv": "16.4.5", "fastify": "^4.0.3", "ioredis": "5.4.2", "jsonc-eslint-parser": "2.4.0", "jsonwebtoken": "^8.0.0", "nanoid": "^4.0.2"}, "devDependencies": {"@commitlint/config-conventional": "^17.8.1", "@stylistic/eslint-plugin": "^1.8.1", "@types/better-sqlite3": "7.6.11", "@types/jsonwebtoken": "8.5.9", "@types/node": "^16.18.0", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "concurrently": "8.2.2", "eslint": "^8.56.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsonc": "^2.16.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-unused-imports": "^4.1.4", "knip": "^2.43.0", "prettier": "^3.3.3", "simple-git-hooks": "^2.11.1", "tsx": "3.14.0", "typescript": "5.2.2"}, "engines": {"node": "16.19.1", "npm": ">=9.0.0"}, "name": "flash-preview-server", "scripts": {"docker:up": "docker-compose -f ci/docker-compose.yml up --build --force-recreate", "build": "tsc --project tsconfig.json --pretty", "build:watch": "tsc --project tsconfig.json --watch", "dev:server": "tsx watch src/index.ts", "dev:redis": "redis-server", "dev:queue": "tsx watch src/queues/worker.ts", "dev": "concurrently \"npm run dev:redis\" \"npm run dev:server\" \"npm run dev:queue\"", "check": "npm run check:types && npm run check:lint && npm run check:format && npm run check:unused", "check:format": "prettier . --check --config ci/.prettierrc.json --ignore-path ci/.prettierignore", "check:lint": "eslint . --quiet --config ci/.eslintrc.json --ignore-path ci/.eslintignore", "check:types": "tsc --noEmit --pretty -p tsconfig.json", "check:unused": "knip --config ci/knip.json", "fix": "npm run check:types && npm run fix:lint && npm run fix:format", "fix:format": "npx -y prettier --cache . --write --list-different --config ci/.prettierrc.json --ignore-path ci/.prettierignore", "fix:lint": "npx -y eslint --quiet --cache . --fix  --config ci/.eslintrc.json --ignore-path ci/.eslintignore", "postinstall": "simple-git-hooks ci/.simple-git-hooks.json", "start": "node dist/index.js", "update-deps": "npx -y npm-check-updates --configFilePath ci/.ncurc.json"}, "type": "module", "version": "1.0.0"}